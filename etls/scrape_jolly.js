const fs = require('fs');
const path = require('path');

// Function to create request body with specific page number
function createRequestBody(pageNo) {
    return `SearchParameters.Id=54866&SearchParameters.StartDate=&SearchParameters.EndDate=&SearchParameters.SearchType=Location&SearchParameters.HotelType=Domestic&SearchParameters.Name=Bodrum&SearchParameters.AutoCompleteId=54866&SearchParameters.Discount=None&SearchParameters.DefaultFilterValue=54866-Di&SearchParameters.UnChangeableFilterValue=38365-Ci&SearchParameters.PageNo=${pageNo}&SearchParameters.RequestPath=%2Fbodrum-otelleri&SearchParameters.IsAbroadSearch=False&SearchParameters.IsPackage=False&SearchParameters.IsAdvantagePackage=False&SearchParameters.HasUrlPageNo=False&SearchParameters.IsProductIdList=False&SearchParameters.Destination.Id=54866&SearchParameters.Destination.Name=Bodrum&SearchParameters.Destination.CityId=38365&SearchParameters.Destination.CountryId=277&SearchParameters.Destination.DestinationTypeId=5&SearchParameters.OriginId=&SearchParameters.OriginName=&SearchParameters.OriginType=Zone&SearchParameters.LocationType=District&SearchParameters.CustomerTrackId=fd76a8b4-d94c-4d76-9626-5579fbddd462&SearchParameters.Key=&SearchParameters.CrmCode=&SearchParameters.TraceId=0HNE6GA8VIGH2%3A0000004B&SearchParameters.RequestKey=Zcm5Ve6bCRbUX%2B%2B2wf83eM1HYDXD7Ai6bnPCTLNVR3qluA7g%2B2HaDI1u%2BvVxMdzK9s%2FWQNiGY3DkT0ZP8%2FsjvxytI9TUC6U79ygOkRzXZAKHkjTBNTx%2BDvJWQ96jAbxG&filters=Location%3A54866-Di&order=&pageNo=${pageNo}&wordSort=&rooms=1&packageSearchType=3&rawData=true`;
}

// Function to fetch data from a specific page
async function fetchPage(pageNo) {
    const response = await fetch("https://www.jollytur.com/hotel/GetListPagePartials", {
        "headers": {
            "accept": "*/*",
            "accept-language": "tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7",
            "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
            "priority": "u=1, i",
            "request-context": "appId=cid-v1:5b52d9e9-4a6f-48d3-89c5-2eeb57d24111",
            "request-id": "|VJjbM.vymCV",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"macOS\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "x-requested-with": "XMLHttpRequest",
            "cookie": "notification=; CustomerTrackId=fd76a8b4-d94c-4d76-9626-5579fbddd462; .JollyB2C.Session=CfDJ8JQHn%2BUBdm9PvoAHb5DCzANFzneQ8U4oBnWtDJifMCysr%2FWPLL6sJH5F0x8urdro1vJ3UdqkqyCQdzySZVAJ6KgZl%2FiLiU%2BMop7jleCtMHa5NeKi9UigILaR%2BaZGTsDWeOeyuF%2BUVsppnUoj2X1teLVlXah83yuehOQsCLoQt0pb; sourcetrack1=1; _ym_uid=**********9165515; _ym_d=**********; ai_user=7c6Sk|2025-07-18T12:42:08.172Z; _gcl_au=1.1.**********.**********; _ga=GA1.1.**********.**********; _tt_enable_cookie=1; _ttp=01K0ERWVGR8ZXVNZ8GHKJWPK1F_.tt.1; .AspNetCore.Antiforgery._Lg8ymgzfD0=CfDJ8JQHn-UBdm9PvoAHb5DCzANra-PSrorJRpp2QrmfyX7VZHemUMAosAH6niEdyEZWDzrM1hwdo9isBqQs8zyibDsV5Y_gA18JwfypnhsKJQdVxMMIpx4Hf2gUVwcq-z4PYG5AXXow8nQNyemgsJOVp0g; sourcetrackset=utmcsr=direct-utmcmd; _hjSessionUser_229404=********************************************************************************************************************; _hjSession_229404=eyJpZCI6IjI4NGNhMjk3LWNiNzUtNGQzNi05NTBmLTkzMDY4ODY1ZWYzNiIsImMiOjE3NTI5NDI4NDE5NDcsInMiOjAsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowLCJzcCI6MH0=; _ym_isad=1; _clck=1leu69i%7C2%7Cfxq%7C0%7C2025; CustomerTrackData%3Afd76a8b4-d94c-4d76-9626-5579fbddd462=zQUAAB%2BLCAAAAAAAAAulU9tu2kAQ%2FZd9LduuL%2Fj2Rk2qIrUBgV%2BiKA9r7xhWmHW0XkdFKP%2FeWdsBJ2mp2koIMbNzzsyZOdyfyEKQhLhR6LC8AJqXeUT9yBc05qFHo5zzIIjCaBoXZELStjH1AXSmebHvgKUIAx7lPhWxX1AfIxoHbkCn0zAucyGEH7gI3EDTyFp1EMcJC14yTmOvZJRBENKgKD0aizL3XIBIeL7tpYEbEHP8sgMyd0pZSJ04c%2BLE8xMn%2FhhMPdcNog%2FMSxjrmlRQIKSfzsChIcn9iWSgDyRRbVVdSuxrBj8MMn%2BuhW4P5PWbHXTqR0EwId%2Bk2q%2B42WHpp7yrpbWBqgIt34Cy4yOO6k7ICvvzLWyA62LXZ23754cJ6SMHgXWrCzhDvlrObnCSnMiNMpqrAgbxjDGHdp%2BMMdTay013UOzr1lwt6njXdX3ouPuFzERbmbRulekmSXeyEkPIhnC2BVtrB64Nr1a8aUBtQb%2BgrJRbud2NaOb8OAQoZ9HYqSq8er8EkhjdwrOla%2FVZ5gp3WIu%2FnB87r2qpzLKcwyPXptWIZkj9pbIDncmXWm5l5zgU1Qe3%2FDCc4iWzmGWztBbn7KLp86k0R5KUvGoAlUFjpOJmMDB7lRlzjgvfEY8e37APKq7uYQ1Yoq6WjM%2F6csdLuFAlV6PnjWkFjBPLJ9Bo2hHku6yk4fp4ydzhr%2B2oBc%2Bl6i2M0Rq9OBjaniPleo0N%2BMXVK1ns28f%2FlXllyfM%2Fn6W%2F39DmXf7XpviteSwIlc4KI5%2FwpGehG9za9b%2Fljbpu%2B3%2F4bz0%2F%2FATBW%2FMyzQUAAA%3D%3D; __rtbh.lid=%7B%22eventType%22%3A%22lid%22%2C%22id%22%3A%22HnuD8hdHp4ECMKG5W1nz%22%2C%22expiryDate%22%3A%222026-07-19T16%3A34%3A21.685Z%22%7D; __rtbh.uid=%7B%22eventType%22%3A%22uid%22%2C%22id%22%3A%22%22%2C%22expiryDate%22%3A%222026-07-19T16%3A34%3A21.686Z%22%7D; _uetsid=2dac7c8064be11f08dbfc547de89f15b; _uetvid=9ee21d7063d411f08d76875e2d872429; ttcsid_CSD3GEBC77U8G0CR9OLG=1752942842165::PZBi9RWgAaKfLCz_91Qh.3.1752942920173; ttcsid=1752942842166::NmzJ4HpBhGG77XH7c-A0.3.1752942920173; clicktoCallCt=yes; _clsk=1b6i2t8%7C1752942920570%7C3%7C1%7Ck.clarity.ms%2Fcollect; _ga_T4W7WKY7GW=GS2.1.s1752942842$o4$g1$t1752942921$j57$l0$h0; ai_session=AFIHj|1752942846264|1752942931869.1",
            "Referer": `https://www.jollytur.com/bodrum-otelleri?Sayfa=${pageNo}`
        },
        "body": createRequestBody(pageNo),
        "method": "POST"
    });
    console.log(`Response status for page ${pageNo}: ${response.status}`);
    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
}

// Main function to scrape all pages
async function scrapeAllPages() {
    const allData = [];
    let currentPage = 1;
    let hasMoreData = true;

    console.log('Starting to scrape all pages...');

    while (hasMoreData) {
        try {
            console.log(`Fetching page ${currentPage}...`);
            const pageData = await fetchPage(currentPage);


            // Check if page has data (you may need to adjust this condition based on the actual response structure)
            if (pageData && pageData.data && pageData.data.length > 0) {
                allData.push(...pageData.data);
                console.log(`Page ${currentPage}: Found ${pageData.data.length} records`);

                // If we got less than 20 records, we've reached the end
                if (pageData.data.length < 20) {
                    hasMoreData = false;
                }
            } else {
                // No more data available
                hasMoreData = false;
                console.log(`Page ${currentPage}: No more data found`);
            }

            currentPage++;

            // Add a small delay to be respectful to the server
            await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (error) {
            console.error(`Error fetching page ${currentPage}:`, error);
            hasMoreData = false;
        }
    }

    console.log(`Scraping completed. Total records collected: ${allData.length}`);

    // Save all collected data to file
    const filePath = path.join(__dirname, 'jolly_all_data.json');
    const dataToSave = JSON.stringify({
        totalRecords: allData.length,
        totalPages: currentPage - 1,
        data: allData,
        scrapedAt: new Date().toISOString()
    }, null, 2);

    fs.writeFileSync(filePath, dataToSave, 'utf8');
    console.log(`All data saved to jolly_all_data.json (${allData.length} records)`);

    return allData;
}

// Execute the scraping
scrapeAllPages()
    .then(data => {
        console.log('Scraping process completed successfully');
    })
    .catch(error => {
        console.error('Scraping process failed:', error);
    });