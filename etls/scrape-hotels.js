const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch({ headless: false }); // headless: true yaparsan arka planda çalışır
  const context = await browser.newContext({
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36'
  });
  const page = await context.newPage();

  // Web sayfasına git
  const url = 'https://www.jollytur.com/bodrum-otelleri ';
  await page.goto(url, { waitUntil: 'networkidle' });

  // Daha fazla tesis göster butonuna tıklayarak tüm otelleri yükle
  let moreButtonVisible = true;
  while (moreButtonVisible) {
    const moreButton = page.locator('button:has-text("Daha fazla tesis göster")');

    try {
      await moreButton.scrollIntoViewIfNeeded({ timeout: 3000 });
      await moreButton.click({ timeout: 5000 });
      await page.waitForTimeout(2000); // Yeni otellerin yüklenmesi için bekle
    } catch (e) {
      console.log('Tüm oteller yüklendi veya buton bulunamadı.');
      moreButtonVisible = false;
    }
  }

  // Otelleri çek
  const hotels = await page.$$eval('.col-xs-3 a', links => {
    return links.map(link => ({
      name: link.innerText.trim(),
      url: link.href,
    }));
  });

  // JSON olarak yazdır
  console.log(JSON.stringify(hotels, null, 2));

  // Tarayıcıyı kapat
  await context.close();
  await browser.close();
})();